abstract,title,issue,pdf_link,category,year,keywords,external_link,featured,journal,volume,doi,id,authors,pages,pdf_path,is_published
TBD,Knowledge Scaffolding Recommendation System for Supervising Term Papers,Rank B,/publications/healthcare-ml.pdf,Generative AI,2025,"Large Language Models, Education, Innovation, Recommendation Systems",,FALSE,"20th European Conference on Technology Enhanced Learning (ECTEL 2025). Springer LNCS. 15-19 September 2025. Newcastle and Durham, United Kingdom",Conference,,1,"<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>",Acceptance Ratio 27%,,FALSE
TBD,TPRS: AI-Assisted Research Topic Refinement for Distance Learners,Rank A,/publications/web-frameworks.pdf,Generative AI,2025,"Recommendation Systems, Generative AI, LLM, RAG, VectorDB",,FALSE,"26th International Conference on Artificial Intelligence in Education (AIED 2025). July 22-26, 2025, Palermo, Italy",Conference,,2,"<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>wart",,,FALSE
TBD,LLM-based Literature Recommender System in Higher Education — A Case Study of Supervising Students’ Term Papers,Rank A,/publications/db-microservices.pdf,Generative AI,2025,"Databases, Microservices, Performance Optimization, Sharding",,FALSE,"2nd International Workshop on Generative AI and Learning Analytics (GenAI-LA): Evidence of Impacts on Human Learning, 15th International Learning Analytics and Knowledge Conference (LAK'25). March 3-7, 2025, Dublin, Ireland",Conference,,3,"Xia Wang, Nghia Duong-Trung, Rahul R. Bhoyar, Angelin Mary Jose",,,FALSE
"Searching appropriate experimental datasets for machine learning projects and reducing the need for one-on-one student-teacher consultations are both challenging. Despite over 50,000 different datasets available across multiple domains on websites like Kaggle, practitioners often need help locating the necessary datasets. Even with the aid of Kaggle’s API and web search functionalities, the search results are not organized meaningfully to a specific context. Recent developments in artificial intelligence (AI) and large language models (LLMs) provide new means of addressing these relevant issues, which were impossible before. This paper introduces KaggleGPT, an LLM- assisted conversational recommender system designed to streamline finding suitable datasets for students’ projects directly from the textual content. The core of KaggleGPT employs a comprehensive approach by integrating profile-based, expert-based, knowledge-based, and multi-criteria-based recommendation engines. Our vision is for educators and students using KaggleGPT to enhance the educational experience and make dataset discovery more efficient and user-friendly.",KaggleGPT: Prompt-based Recommender System for Efficient Dataset Discovery,,/publications/nlp-sentiment.pdf,Generative AI,2024,"Kaggle, Recommender System, Prompt-based Recommendation, Large Language Models, Dataset Discovery",https://dl.gi.de/items/f392a307-713f-4714-a842-f83dd2d8d755,TRUE,"EduRS - Recommender Systems in Education at DELFI Conference on Educational Technologies. September 09-11, 2024. Fulda, Germany",Gesellschaft für Informatik e.V.,10.18420/delfi2024-ws-30,4,"Rahul Rajkumar Bhoyar, Xia Wang, Nghia Duong-Trung",207-214,/data/publications/kagglegpt.pdf,TRUE