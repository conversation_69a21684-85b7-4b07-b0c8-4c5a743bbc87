"""
SQLAlchemy models for the portfolio database.
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, JSON
from sqlalchemy.sql import func
from .connection import Base


class Skill(Base):
    __tablename__ = "skills"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    level = Column(Integer, nullable=False)  # 1-100
    category = Column(String(50), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class Experience(Base):
    __tablename__ = "experiences"
    
    id = Column(Integer, primary_key=True, index=True)
    company = Column(String(200), nullable=False)
    position = Column(String(200), nullable=False)
    start_date = Column(String(50), nullable=False)
    end_date = Column(String(50))
    description = Column(Text)
    technologies = Column(JSON)  # List of technologies
    achievements = Column(JSON)  # List of achievements
    logo = Column(String(500))
    company_url = Column(String(500))
    location = Column(String(200))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class Education(Base):
    __tablename__ = "education"
    
    id = Column(Integer, primary_key=True, index=True)
    degree = Column(String(200), nullable=False)
    field = Column(String(200), nullable=False)
    institution = Column(String(300), nullable=False)
    start_date = Column(String(50), nullable=False)
    end_date = Column(String(50))
    description = Column(Text)
    achievements = Column(JSON)  # List of achievements
    logo = Column(String(500))
    institution_url = Column(String(500))
    location = Column(String(200))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class Certification(Base):
    __tablename__ = "certifications"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(300), nullable=False)
    issuer = Column(String(200), nullable=False)
    date = Column(String(50), nullable=False)
    icon = Column(String(500))
    verification_url = Column(String(500))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class Project(Base):
    __tablename__ = "projects"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(300), nullable=False)
    description = Column(Text)
    technologies = Column(JSON)  # List of technologies
    category = Column(String(100), nullable=False)
    github_url = Column(String(500))
    live_url = Column(String(500))
    date = Column(String(50))
    image_url = Column(String(500))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class Publication(Base):
    __tablename__ = "publications"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(500), nullable=False)
    authors = Column(Text, nullable=False)
    journal = Column(String(500), nullable=False)
    year = Column(String(10), nullable=False)
    volume = Column(String(50))
    issue = Column(String(50))
    pages = Column(String(50))
    doi = Column(String(200))
    abstract = Column(Text)
    keywords = Column(JSON)  # List of keywords
    pdf_link = Column(String(500))
    external_link = Column(String(500))
    category = Column(String(100))
    featured = Column(Boolean, default=False)
    pdf_path = Column(String(500))
    is_published = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class BlogPost(Base):
    __tablename__ = "blog_posts"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(300), nullable=False)
    excerpt = Column(Text)
    date = Column(String(50), nullable=False)
    author = Column(String(200), nullable=False)
    category = Column(String(100), nullable=False)
    tags = Column(JSON)  # List of tags
    image = Column(String(500))
    featured = Column(Boolean, default=False)
    content = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class ContactMessage(Base):
    __tablename__ = "contact_messages"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200), nullable=False)
    email = Column(String(300), nullable=False)
    subject = Column(String(500), nullable=False)
    message = Column(Text, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    processed = Column(Boolean, default=False)


class ChatLog(Base):
    __tablename__ = "chat_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_message = Column(Text, nullable=False)
    bot_response = Column(Text, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    session_id = Column(String(100))  # Optional session tracking
