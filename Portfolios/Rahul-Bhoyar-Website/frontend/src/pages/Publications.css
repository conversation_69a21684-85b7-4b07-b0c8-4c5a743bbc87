/* Publications Page Styles */
.publications-page {
  padding-top: var(--space-16);
}

/* Publications Hero Section */
.publications-hero {
  padding: var(--space-10) 0;
  background-color: var(--bg-secondary);
  position: relative;
  overflow: hidden;
}

.publications-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 80% 20%, rgba(var(--primary-900-rgb), 0.1), transparent 50%),
    radial-gradient(circle at 20% 80%, rgba(var(--primary-900-rgb), 0.1), transparent 50%);
  z-index: 0;
}

.publications-hero > * {
  position: relative;
  z-index: 1;
}

.publications-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.publications-subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
}

.search-bar {
  max-width: 600px;
  margin: 0 auto;
  box-shadow: var(--shadow-md);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.search-bar .form-control {
  padding: var(--space-3);
  border: none;
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.search-bar .input-group-text {
  background-color: var(--card-bg);
  border: none;
  color: var(--text-secondary);
}

/* Featured Publications Section */
.featured-publications {
  padding: var(--space-10) 0;
}

/* All Publications Section */
.all-publications {
  padding: var(--space-10) 0;
  background-color: var(--bg-primary);
}

.publications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.publications-count {
  color: var(--text-tertiary);
  font-size: var(--font-size-md);
}

/* Publication Card */
.publication-card {
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  margin-bottom: var(--space-6);
  border: 1px solid var(--border-color);
  transition: all 0.4s ease;
  position: relative;
}

.publication-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
  z-index: 1;
}

.publication-card:hover::before {
  transform: scaleX(1);
}

.publication-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: transparent;
}

.publication-card.featured {
  height: 100%;
}

.publication-header {
  padding: var(--space-5);
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.publication-year {
  position: absolute;
  top: var(--space-5);
  right: var(--space-5);
  background: var(--gradient-primary);
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.publication-title {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-3);
  color: var(--text-primary);
  font-weight: var(--font-weight-bold);
  padding-right: var(--space-10);
}

.publication-authors {
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
  font-weight: var(--font-weight-medium);
}

.publication-journal {
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  margin-bottom: 0;
}

.publication-body {
  padding: var(--space-5);
}

.publication-abstract {
  position: relative;
  padding-left: var(--space-8);
  margin-bottom: var(--space-4);
}

.quote-icon {
  position: absolute;
  top: 0;
  left: 0;
  font-size: var(--font-size-2xl);
  color: var(--primary-500);
  opacity: 0.5;
}

.publication-abstract p {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

.publication-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.publication-keyword {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  transition: all 0.3s ease;
}

.publication-keyword:hover {
  background-color: var(--primary-900);
  color: white;
}

.publication-links {
  display: flex;
  gap: var(--space-3);
}

.publication-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.publication-link.pdf {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.publication-link.pdf:hover {
  background-color: var(--accent-red);
  color: white;
}

.publication-link.external {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.publication-link.external:hover {
  background-color: var(--primary-900);
  color: white;
}

/* Disabled publication links */
.publication-link.disabled {
  background-color: var(--bg-secondary) !important;
  color: var(--text-muted) !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.publication-link.disabled:hover {
  background-color: var(--bg-secondary) !important;
  color: var(--text-muted) !important;
  transform: none !important;
}

/* Publications Sidebar */
.publications-sidebar {
  position: sticky;
  top: 100px;
}

.sidebar-widget {
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-5);
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
}

.sidebar-widget h4 {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-4);
  color: var(--text-primary);
  position: relative;
  padding-bottom: var(--space-2);
}

.sidebar-widget h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-list li {
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-list li:last-child {
  border-bottom: none;
}

.category-list li:hover,
.category-list li.active {
  color: var(--primary-900);
  padding-left: var(--space-2);
}

.category-list li.active {
  font-weight: var(--font-weight-semibold);
}

/* Metrics */
.metrics-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
}

.metric-item {
  flex: 1;
  min-width: 80px;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  text-align: center;
  transition: all 0.3s ease;
}

.metric-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  background: var(--gradient-primary);
}

.metric-item:hover .metric-value,
.metric-item:hover .metric-label {
  color: white;
}

.metric-value {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  transition: all 0.3s ease;
}

.metric-label {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  transition: all 0.3s ease;
}

.no-results {
  text-align: center;
  padding: var(--space-10);
  color: var(--text-secondary);
}

/* Responsive */
@media (max-width: 992px) {
  .publications-sidebar {
    margin-top: var(--space-6);
    position: static;
  }
}

@media (max-width: 768px) {
  .publications-title {
    font-size: var(--font-size-3xl);
  }
  
  .publications-subtitle {
    font-size: var(--font-size-lg);
  }
}
